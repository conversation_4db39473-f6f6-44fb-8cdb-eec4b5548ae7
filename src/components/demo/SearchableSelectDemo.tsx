import React, { useState } from 'react';
import { SearchableSelect } from '@/components/ui/searchable-select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

const demoOptions = [
  { label: "Apple", value: "apple" },
  { label: "Banana", value: "banana" },
  { label: "Cherry", value: "cherry" },
  { label: "Date", value: "date" },
  { label: "Elderberry", value: "elderberry" },
  { label: "Fig", value: "fig" },
  { label: "Grape", value: "grape" },
  { label: "Honeydew", value: "honeydew" },
  { label: "<PERSON>wi", value: "kiwi" },
  { label: "Lemon", value: "lemon" },
  { label: "Mango", value: "mango" },
  { label: "Orange", value: "orange" },
  { label: "Papaya", value: "papaya" },
  { label: "Quince", value: "quince" },
  { label: "Raspberry", value: "raspberry" },
  { label: "Strawberry", value: "strawberry" },
  { label: "Tangerine", value: "tangerine" },
  { label: "Watermelon", value: "watermelon" },
];

export const SearchableSelectDemo = () => {
  const [selectedValue, setSelectedValue] = useState<string>("");

  return (
    <div className="p-6 space-y-6 max-w-4xl mx-auto">
      <Card>
        <CardHeader>
          <CardTitle>Improved SearchableSelect - Combobox Behavior</CardTitle>
          <CardDescription>
            Test the new combobox behavior with immediate dropdown opening, real-time filtering, and full keyboard navigation
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-8">
          <div className="space-y-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Searchable Combobox</label>
              <SearchableSelect
                options={demoOptions}
                value={selectedValue}
                onValueChange={setSelectedValue}
                placeholder="Click or type to search fruits..."
                searchPlaceholder="Search fruits..."
                className="w-full max-w-md"
              />
              <p className="text-xs text-muted-foreground">
                Selected: {selectedValue || 'None'}
              </p>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Non-searchable (readonly input)</label>
              <SearchableSelect
                options={demoOptions.slice(0, 5)}
                value={selectedValue}
                onValueChange={setSelectedValue}
                placeholder="Click to select..."
                searchable={false}
                className="w-full max-w-md"
              />
            </div>
          </div>

          <div className="bg-blue-50 dark:bg-blue-950 p-4 rounded-lg">
            <h4 className="font-medium text-sm mb-2">Test Instructions:</h4>
            <ul className="text-xs space-y-1 text-muted-foreground">
              <li>1. Click into the input field - dropdown should open immediately</li>
              <li>2. Start typing (e.g., "ap") - options should filter in real-time</li>
              <li>3. Use ↑ and ↓ arrow keys to navigate options</li>
              <li>4. Press Enter to select the highlighted option</li>
              <li>5. Press Escape to close the dropdown</li>
              <li>6. Click on any option to select it with mouse</li>
            </ul>
          </div>

          <div className="bg-green-50 dark:bg-green-950 p-4 rounded-lg">
            <h4 className="font-medium text-sm mb-2">Features implemented:</h4>
            <ul className="text-xs space-y-1 text-muted-foreground">
              <li>✅ Opens on click or typing (no Enter required)</li>
              <li>✅ Real-time filtering as you type</li>
              <li>✅ Full keyboard navigation (↑/↓/Enter/Escape)</li>
              <li>✅ Visual highlighting of focused option</li>
              <li>✅ Mouse interaction preserved</li>
              <li>✅ Proper ARIA roles for accessibility (combobox, listbox, option)</li>
              <li>✅ Maintains existing SearchableSelect API</li>
              <li>✅ Supports both searchable and non-searchable modes</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
