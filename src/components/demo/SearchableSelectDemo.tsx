import React, { useState } from 'react';
import { SearchableSelect } from '@/components/ui/searchable-select';
import { SearchableMultiSelect } from '@/components/ui/searchable-multi-select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

const demoOptions = [
  { label: 'Apple', value: 'apple' },
  { label: 'Banana', value: 'banana' },
  { label: 'Cherry', value: 'cherry' },
  { label: 'Date', value: 'date' },
  { label: 'Elderberry', value: 'elderberry' },
  { label: 'Fig', value: 'fig' },
  { label: 'Grape', value: 'grape' },
  { label: 'Honeydew', value: 'honeydew' },
  { label: 'Kiwi', value: 'kiwi' },
  { label: 'Lemon', value: 'lemon' },
  { label: 'Mango', value: 'mango' },
  { label: 'Orange', value: 'orange' },
  { label: 'Papaya', value: 'papaya' },
  { label: 'Quince', value: 'quince' },
  { label: 'Raspberry', value: 'raspberry' },
  { label: 'Strawberry', value: 'strawberry' },
  { label: 'Tangerine', value: 'tangerine' },
  { label: 'Watermelon', value: 'watermelon' },
];

export const SearchableSelectDemo = () => {
  const [singleValue, setSingleValue] = useState<string>('');
  const [multiValue, setMultiValue] = useState<string[]>([]);

  return (
    <div className="p-6 space-y-6 max-w-2xl mx-auto">
      <Card>
        <CardHeader>
          <CardTitle>Enhanced SearchableSelect Demo</CardTitle>
          <CardDescription>
            Test the new behavior: Click or start typing to open dropdown, real-time filtering, keyboard navigation
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-2">
            <label className="text-sm font-medium">Single Select</label>
            <SearchableSelect
              options={demoOptions}
              value={singleValue}
              onValueChange={setSingleValue}
              placeholder="Click or type to search fruits..."
              searchPlaceholder="Search fruits..."
            />
            <p className="text-xs text-muted-foreground">
              Selected: {singleValue || 'None'}
            </p>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Multi Select</label>
            <SearchableMultiSelect
              options={demoOptions}
              value={multiValue}
              onValueChange={setMultiValue}
              placeholder="Click or type to search fruits..."
              searchPlaceholder="Search fruits..."
            />
            <p className="text-xs text-muted-foreground">
              Selected: {multiValue.length > 0 ? multiValue.join(', ') : 'None'}
            </p>
          </div>

          <div className="bg-blue-50 dark:bg-blue-950 p-4 rounded-lg">
            <h4 className="font-medium text-sm mb-2">How to test:</h4>
            <ul className="text-xs space-y-1 text-muted-foreground">
              <li>• <strong>Click</strong> the select input → dropdown opens immediately</li>
              <li>• <strong>Start typing</strong> → dropdown opens and filters in real-time</li>
              <li>• <strong>↑/↓ arrows</strong> → navigate through options</li>
              <li>• <strong>Enter</strong> → select highlighted option</li>
              <li>• <strong>Escape</strong> → close dropdown</li>
              <li>• <strong>Mouse hover</strong> → updates keyboard highlight</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
