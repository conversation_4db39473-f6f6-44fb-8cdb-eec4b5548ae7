import React, { useState } from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { SearchableSelect } from '@/components/ui/searchable-select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

const demoOptions = [
  { label: 'Apple', value: 'apple' },
  { label: 'Banana', value: 'banana' },
  { label: 'Cherry', value: 'cherry' },
  { label: 'Date', value: 'date' },
  { label: 'Elderberry', value: 'elderberry' },
  { label: 'Fig', value: 'fig' },
  { label: 'Grape', value: 'grape' },
  { label: 'Honeydew', value: 'honeydew' },
  { label: 'Kiwi', value: 'kiwi' },
  { label: 'Lemon', value: 'lemon' },
  { label: 'Mango', value: 'mango' },
  { label: 'Orange', value: 'orange' },
  { label: 'Papaya', value: 'papaya' },
  { label: 'Quince', value: 'quince' },
  { label: 'Ra<PERSON>berry', value: 'raspberry' },
  { label: 'Strawberry', value: 'strawberry' },
  { label: 'Tangerine', value: 'tangerine' },
  { label: 'Watermelon', value: 'watermelon' },
];

export const ComboboxSelectDemo = () => {
  const [selectValue, setSelectValue] = useState<string>('');
  const [searchableValue, setSearchableValue] = useState<string>('');

  return (
    <div className="p-6 space-y-6 max-w-4xl mx-auto">
      <Card>
        <CardHeader>
          <CardTitle>Select Component Comparison</CardTitle>
          <CardDescription>
            Compare the new combobox-style Select with the existing SearchableSelect component
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-8">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* New Combobox Select */}
            <div className="space-y-2">
              <label className="text-sm font-medium">New Combobox Select</label>
              <Select value={selectValue} onValueChange={setSelectValue}>
                <SelectTrigger>
                  <SelectValue placeholder="Click or type to search fruits..." />
                </SelectTrigger>
                <SelectContent>
                  {demoOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <p className="text-xs text-muted-foreground">
                Selected: {selectValue || 'None'}
              </p>
            </div>

            {/* Existing SearchableSelect */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Existing SearchableSelect</label>
              <SearchableSelect
                options={demoOptions}
                value={searchableValue}
                onValueChange={setSearchableValue}
                placeholder="Click or type to search fruits..."
                searchPlaceholder="Search fruits..."
              />
              <p className="text-xs text-muted-foreground">
                Selected: {searchableValue || 'None'}
              </p>
            </div>
          </div>

          <div className="bg-blue-50 dark:bg-blue-950 p-4 rounded-lg">
            <h4 className="font-medium text-sm mb-2">How to test:</h4>
            <ul className="text-xs space-y-1 text-muted-foreground">
              <li>• <strong>Click</strong> the select input → dropdown opens immediately</li>
              <li>• <strong>Start typing</strong> → dropdown opens and filters in real-time</li>
              <li>• <strong>↑/↓ arrows</strong> → navigate through options</li>
              <li>• <strong>Enter</strong> → select highlighted option</li>
              <li>• <strong>Escape</strong> → close dropdown</li>
              <li>• <strong>Mouse hover</strong> → updates keyboard highlight</li>
            </ul>
          </div>

          <div className="bg-green-50 dark:bg-green-950 p-4 rounded-lg">
            <h4 className="font-medium text-sm mb-2">Features implemented:</h4>
            <ul className="text-xs space-y-1 text-muted-foreground">
              <li>✅ Opens on click or typing</li>
              <li>✅ Real-time filtering as you type</li>
              <li>✅ Full keyboard navigation (↑/↓/Enter/Escape)</li>
              <li>✅ Mouse interaction preserved</li>
              <li>✅ Proper ARIA roles for accessibility</li>
              <li>✅ Maintains existing Select API</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
