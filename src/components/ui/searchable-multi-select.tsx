import * as React from "react";
import { Check, ChevronsUpDown, X } from "lucide-react";
import { cn } from "@/lib/utils/cn";
import { Button } from "@/components/ui/button";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Badge } from "@/components/ui/badge";

export interface SearchableMultiSelectOption {
  label: string;
  value: string;
  disabled?: boolean;
}

interface SearchableMultiSelectProps {
  options: SearchableMultiSelectOption[];
  value?: string[];
  onValueChange?: (values: string[]) => void;
  placeholder?: string;
  searchPlaceholder?: string;
  emptyMessage?: string;
  disabled?: boolean;
  searchable?: boolean;
  maxSelected?: number;
  showSelectedCount?: boolean;
  className?: string;
}

export const SearchableMultiSelect = React.forwardRef<
  React.ElementRef<typeof Button>,
  SearchableMultiSelectProps
>(({
  options = [],
  value = [],
  onValueChange,
  placeholder = "Pilih opsi...",
  searchPlaceholder = "Cari...",
  emptyMessage = "Tidak ditemukan",
  disabled = false,
  searchable = true,
  maxSelected,
  showSelectedCount = true,
  className,
  ...props
}, ref) => {
  const [open, setOpen] = React.useState(false);
  const [searchValue, setSearchValue] = React.useState("");
  const [highlightedIndex, setHighlightedIndex] = React.useState(-1);

  // Refs for managing focus and scroll
  const searchInputRef = React.useRef<HTMLInputElement>(null);
  const optionRefs = React.useRef<(HTMLDivElement | null)[]>([]);

  const selectedOptions = React.useMemo(() => 
    options.filter(option => value.includes(option.value)),
    [options, value]
  );

  const filteredOptions = React.useMemo(() => {
    if (!searchValue) return options;
    return options.filter(option =>
      option.label.toLowerCase().includes(searchValue.toLowerCase()) ||
      option.value.toLowerCase().includes(searchValue.toLowerCase())
    );
  }, [options, searchValue]);

  // Reset highlighted index when filtered options change
  React.useEffect(() => {
    setHighlightedIndex(-1);
  }, [filteredOptions]);

  // Focus search input when dropdown opens
  React.useEffect(() => {
    if (open && searchable && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [open, searchable]);

  // Handle search input change - opens dropdown and filters in real-time
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setSearchValue(newValue);

    // Open dropdown immediately when user starts typing
    if (!open) {
      setOpen(true);
    }
  };

  // Handle trigger click - opens dropdown immediately
  const handleTriggerClick = () => {
    if (!disabled) {
      setOpen(true);
      // Focus search input after opening
      setTimeout(() => {
        if (searchInputRef.current) {
          searchInputRef.current.focus();
        }
      }, 0);
    }
  };

  const handleSelect = (selectedValue: string) => {
    const newValue = value.includes(selectedValue)
      ? value.filter(v => v !== selectedValue)
      : [...value, selectedValue];
    onValueChange?.(newValue);
  };

  const handleRemove = (valueToRemove: string, event: React.MouseEvent) => {
    event.stopPropagation();
    const newValue = value.filter(v => v !== valueToRemove);
    onValueChange?.(newValue);
  };

  const handleOpenChange = (newOpen: boolean) => {
    // Only allow closing, opening is handled by click/typing
    if (!newOpen) {
      setOpen(false);
      setSearchValue("");
      setHighlightedIndex(-1);
    }
  };

  // Scroll highlighted option into view
  const scrollToHighlighted = (index: number) => {
    const option = optionRefs.current[index];
    if (option) {
      option.scrollIntoView({
        block: 'nearest',
        behavior: 'smooth'
      });
    }
  };

  // Keyboard navigation handler
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!open) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        const nextIndex = highlightedIndex < filteredOptions.length - 1
          ? highlightedIndex + 1
          : 0;
        setHighlightedIndex(nextIndex);
        scrollToHighlighted(nextIndex);
        break;

      case 'ArrowUp':
        e.preventDefault();
        const prevIndex = highlightedIndex > 0
          ? highlightedIndex - 1
          : filteredOptions.length - 1;
        setHighlightedIndex(prevIndex);
        scrollToHighlighted(prevIndex);
        break;

      case 'Enter':
        e.preventDefault();
        if (highlightedIndex >= 0 && highlightedIndex < filteredOptions.length) {
          const selectedOption = filteredOptions[highlightedIndex];
          if (!selectedOption.disabled) {
            handleSelect(selectedOption.value);
          }
        }
        break;

      case 'Escape':
        e.preventDefault();
        setOpen(false);
        break;
    }
  };

  const displayText = React.useMemo(() => {
    if (selectedOptions.length === 0) return placeholder;
    if (selectedOptions.length === 1) return selectedOptions[0].label;
    if (showSelectedCount && selectedOptions.length > 2) {
      return `${selectedOptions.length} dipilih`;
    }
    return selectedOptions.map(opt => opt.label).join(", ");
  }, [selectedOptions, placeholder, showSelectedCount]);

  return (
    <div className="relative">
      <Popover open={open} onOpenChange={handleOpenChange}>
        <PopoverTrigger asChild>
          <Button
            ref={ref}
            variant="outline"
            role="combobox"
            aria-expanded={open}
            aria-haspopup="listbox"
            aria-controls={open ? "searchable-multi-select-listbox" : undefined}
            aria-owns={open ? "searchable-multi-select-listbox" : undefined}
            disabled={disabled}
            onClick={handleTriggerClick}
            className={cn(
              "w-full justify-between h-auto min-h-10 px-3 py-2 text-sm",
              selectedOptions.length === 0 && "text-muted-foreground",
              className
            )}
            {...props}
          >
          <div className="flex flex-wrap items-center gap-1 flex-1 min-w-0">
            {selectedOptions.length === 0 ? (
              <span className="truncate">{placeholder}</span>
            ) : selectedOptions.length <= 2 ? (
              selectedOptions.map((option) => (
                <Badge
                  key={option.value}
                  variant="secondary"
                  className="text-xs h-6 px-2 py-0 gap-1"
                >
                  <span className="truncate max-w-24">{option.label}</span>
                  <X
                    className="h-3 w-3 cursor-pointer hover:text-destructive"
                    onClick={(e) => handleRemove(option.value, e)}
                  />
                </Badge>
              ))
            ) : (
              <>
                <Badge
                  key={selectedOptions[0].value}
                  variant="secondary"
                  className="text-xs h-6 px-2 py-0 gap-1"
                >
                  <span className="truncate max-w-24">{selectedOptions[0].label}</span>
                  <X
                    className="h-3 w-3 cursor-pointer hover:text-destructive"
                    onClick={(e) => handleRemove(selectedOptions[0].value, e)}
                  />
                </Badge>
                {selectedOptions.length > 1 && (
                  <Badge variant="outline" className="text-xs h-6 px-2 py-0">
                    +{selectedOptions.length - 1} lainnya
                  </Badge>
                )}
              </>
            )}
          </div>
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
        <PopoverContent
          className="w-[var(--radix-popover-trigger-width)] p-0"
          align="start"
          onKeyDown={handleKeyDown}
        >
          <div className="flex flex-col max-h-80">
            {searchable && (
              <div className="border-b p-3">
                <input
                  ref={searchInputRef}
                  placeholder={searchPlaceholder}
                  value={searchValue}
                  onChange={handleSearchChange}
                  onKeyDown={handleKeyDown}
                  className="w-full h-9 px-3 py-2 text-sm bg-transparent border border-input rounded-md outline-none placeholder:text-muted-foreground focus:ring-2 focus:ring-ring focus:ring-offset-2"
                  aria-label={searchPlaceholder}
                  aria-autocomplete="list"
                  aria-activedescendant={highlightedIndex >= 0 ? `multi-option-${highlightedIndex}` : undefined}
                />
              </div>
            )}
            <div className="flex-1 overflow-y-auto max-h-60">
              {filteredOptions.length === 0 ? (
                <div className="py-6 text-center text-sm text-muted-foreground">
                  {emptyMessage}
                </div>
              ) : (
                <div
                  className="p-1"
                  role="listbox"
                  id="searchable-multi-select-listbox"
                  aria-label="Options"
                  aria-multiselectable="true"
                >
                  {filteredOptions.map((option, index) => {
                    const isSelected = value.includes(option.value);
                    const isDisabled = option.disabled || (maxSelected && !isSelected && value.length >= maxSelected);

                    return (
                      <div
                        key={option.value}
                        id={`multi-option-${index}`}
                        ref={(el) => (optionRefs.current[index] = el)}
                        onClick={() => !isDisabled && handleSelect(option.value)}
                        onMouseEnter={() => setHighlightedIndex(index)}
                        role="option"
                        aria-selected={isSelected}
                        aria-disabled={isDisabled}
                        className={cn(
                          "flex items-center justify-between px-2 py-2 text-sm rounded-sm cursor-pointer transition-colors",
                          isDisabled
                            ? "opacity-50 cursor-not-allowed"
                            : "hover:bg-accent hover:text-accent-foreground",
                          isSelected && "bg-accent text-accent-foreground",
                          highlightedIndex === index && "bg-blue-100 dark:bg-blue-900"
                        )}
                      >
                        <span className="truncate">{option.label}</span>
                        <Check
                          className={cn(
                            "ml-2 h-4 w-4 shrink-0",
                            isSelected ? "opacity-100" : "opacity-0"
                          )}
                        />
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
});

SearchableMultiSelect.displayName = "SearchableMultiSelect";
