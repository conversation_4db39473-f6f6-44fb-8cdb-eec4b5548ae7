import { useState, useMemo, useEffect } from "react";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { Wallet, DollarSign, Coins, ChevronDown, ChevronRight, Search, ChevronLeft, AlertTriangle } from "lucide-react";
import { formatAmountCurrency } from "@/lib/currency";
import { MoneySummaryGroupByCurrency, MoneySummaryGroupByWallet, MoneySummaryModel } from "@/models/money-summary";

interface MoneySummaryCardProps {
  isLoading?: boolean;
  moneySummaries: MoneySummaryModel[];
}

const MoneySummaryCard = ({
  isLoading,
  moneySummaries
}: MoneySummaryCardProps) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [expandedWallets, setExpandedWallets] = useState<Set<number>>(new Set());
  const itemsPerPage = 5;

  const currencyMap = new Map<string, MoneySummaryGroupByCurrency>();
  for (const row of moneySummaries) {
    const key = row.original_currency_code;
    const existing = currencyMap.get(key);

    if (existing) {
      existing.total_amount += row.total_amount;
    } else {
      currencyMap.set(key, {
        original_currency_code: row.original_currency_code,
        total_amount: row.total_amount,
        base_currency_code: row.base_currency_code,
        latest_rate: row.latest_rate ?? null,
        rate_date: row.rate_date ?? null,
      });
    }
  }
  const currencies = Array.from(currencyMap.keys());

  const walletMap = new Map<string, MoneySummaryGroupByWallet>();
  const walletNames = new Array<string>();
  for (const row of moneySummaries) {
    if (!walletNames.includes(row.wallet_name)) {
      walletNames.push(row.wallet_name);
    }

    const key = `${row.wallet_id}-${row.instrument_id}-${row.asset_id ?? "null"}`;
    const existing = walletMap.get(key);

    if (existing) {
      existing.amount_unit += row.amount_unit;
      existing.amount += row.total_amount;
    } else {
      walletMap.set(key, {
        wallet_id: row.wallet_id,
        wallet_name: row.wallet_name,
        instrument_id: row.instrument_id ?? null,
        instrument_name: row.instrument_name ?? null,
        asset_id: row.asset_id ?? null,
        asset_name: row.asset_name ?? null,
        original_currency_code: row.original_currency_code,
        amount: row.total_amount,
        amount_unit: row.amount_unit ?? 0,
        latest_rate: row.latest_rate ?? null,
        rate_date: row.rate_date ?? null,
        latest_asset_value: row.latest_asset_value ?? null,
        asset_value_date: row.asset_value_date ?? null,
        base_currency_code: row.base_currency_code,
      });
    }
  }

  // Pagination
  const totalPages = Math.ceil(walletNames.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedWallets = walletNames.slice(startIndex, startIndex + itemsPerPage);

  const toggleWalletExpansion = (walletId: number) => {
    const newExpanded = new Set(expandedWallets);
    if (newExpanded.has(walletId)) {
      newExpanded.delete(walletId);
    } else {
      newExpanded.add(walletId);
    }
    setExpandedWallets(newExpanded);
  };

  if (isLoading) {
    return (
      <Card className="p-6">
        <div className="flex items-center gap-2 mb-4">
          <DollarSign className="w-5 h-5 text-green-600" />
          <h3 className="text-lg font-semibold">Ringkasan Keuangan</h3>
        </div>
        <div className="space-y-4">
          <div className="animate-pulse">
            <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          </div>
        </div>
      </Card>
    );
  }

  return (
    <Card className="p-6">
      <div className="flex items-center gap-2 mb-6">
        <DollarSign className="w-5 h-5 text-green-600" />
        <h3 className="text-lg font-semibold">Ringkasan Keuangan</h3>
      </div>

      <div className="space-y-6">
        {/* Grand Totals - Only Original Currency with Base Currency conversion */}
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            <Coins className="w-4 h-4 text-blue-600" />
            <h4 className="font-medium">Total per Mata Uang</h4>
          </div>
          <div className="space-y-2">
            {currencies.length > 0 ? (
              currencies.map((currency) => {
                const currencyData = currencyMap.get(currency);
                const total_amount = currencyData?.total_amount || 0;
                const show_in_base_currency = currencyData?.latest_rate !== null;
                const same_original_and_base_currency = currencyData.original_currency_code === currencyData.base_currency_code;
                const hasNullRate = currencyData?.latest_rate === null && currencyData?.base_currency_code;

                return (
                  <div key={currency} className="p-3 bg-blue-50 rounded-lg">
                    <div className="flex justify-between items-center">
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className="text-xs">
                          {currency}
                        </Badge>
                        {hasNullRate && (
                          <div title="Exchange rate tidak tersedia">
                            <AlertTriangle className="w-4 h-4 text-amber-500" />
                          </div>
                        )}
                      </div>
                      <span className="font-semibold">
                        {formatAmountCurrency(total_amount, currency)}
                      </span>
                    </div>
                    {show_in_base_currency && !same_original_and_base_currency && (
                      <div className="flex justify-between items-center mt-1 text-sm text-muted-foreground">
                        <span>rate terakhir {currencyData?.rate_date}</span>
                        <span>{formatAmountCurrency(total_amount * (currencyData?.latest_rate || 0), currencyData?.base_currency_code)}</span>
                      </div>
                    )}
                    {hasNullRate && (
                      <div className="mt-1 text-xs text-amber-600">
                        Exchange rate tidak tersedia untuk konversi ke {currencyData?.base_currency_code}
                      </div>
                    )}
                  </div>
                );
              })
            ) : (
              <p className="text-sm text-muted-foreground">Belum ada data</p>
            )}
          </div>
        </div>

        {/* Wallet Summaries with Search and Pagination */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Wallet className="w-4 h-4 text-purple-600" />
              <h4 className="font-medium">Ringkasan per Dompet</h4>
            </div>
            <div className="flex items-center gap-2">
              <div className="relative">
                <Search className="w-4 h-4 absolute left-2 top-1/2 transform -translate-y-1/2 text-muted-foreground" />
                <Input
                  placeholder="Cari dompet..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8 w-48"
                />
              </div>
            </div>
          </div>

          {walletNames.length > 0 ? (
            <div className="space-y-4">
              {paginatedWallets.map((wallet) => (
                <Card key={wallet.wallet_id} className="border-l-4 border-l-purple-500">
                  <Collapsible>
                    <CollapsibleTrigger asChild>
                      <div
                        className="p-4 cursor-pointer hover:bg-gray-50 transition-colors"
                        onClick={() => toggleWalletExpansion(wallet.wallet_id)}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            {expandedWallets.has(wallet.wallet_id) ? (
                              <ChevronDown className="w-4 h-4" />
                            ) : (
                              <ChevronRight className="w-4 h-4" />
                            )}
                            <h5 className="font-medium">{wallet.wallet_name}</h5>
                          </div>

                          {/* Wallet totals by original currency */}
                          <div className="flex flex-wrap gap-2">
                            {Object.entries(wallet.totalByOriginalCurrency).map(([currency, amount]) => (
                              <div key={currency} className="text-xs bg-blue-50 px-2 py-1 rounded">
                                <span className="font-medium">{formatAmountCurrency(amount, currency)}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    </CollapsibleTrigger>

                    <CollapsibleContent>
                      <div className="px-4 pb-4 border-t bg-gray-50">
                        <div className="space-y-3 pt-3">
                          {wallet.items.map((item, index) => (
                            <div key={index} className="bg-white p-3 rounded border">
                              <div className="space-y-2">
                                {/* Hierarchy: Goal → Instrument → Asset */}
                                <div className="text-sm">
                                  {item.goal_name && (
                                    <span className="font-medium text-purple-600">{item.goal_name}</span>
                                  )}
                                  {item.instrument_name && (
                                    <span className="text-muted-foreground"> → {item.instrument_name}</span>
                                  )}
                                  {item.asset_name && (
                                    <span className="text-muted-foreground"> → {item.asset_name}</span>
                                  )}
                                </div>

                                {/* Amount display */}
                                <div className="space-y-1">
                                  {/* Amount unit if available */}
                                  {item.amount_unit && (
                                    <div className="text-xs text-muted-foreground">
                                      Unit: {item.amount_unit.toLocaleString("id-ID", { maximumFractionDigits: 4 })}
                                    </div>
                                  )}

                                  {/* Asset value calculation if available */}
                                  {item.latest_asset_value && item.amount_unit ? (
                                    <div className="space-y-1">
                                      <div className="flex justify-between items-center">
                                        <span className="text-sm font-medium">Nilai Aset:</span>
                                        <span className="font-semibold">
                                          {formatAmountCurrency(item.latest_asset_value * item.amount_unit, item.original_currency_code || 'IDR')}
                                        </span>
                                      </div>
                                      {/* Base currency conversion if available */}
                                      {item.latest_rate && item.base_currency_code && (
                                        <div className="flex justify-between items-center text-sm text-muted-foreground">
                                          <span>≈ {item.base_currency_code}:</span>
                                          <span>
                                            {formatAmountCurrency(
                                              item.latest_asset_value * item.amount_unit * item.latest_rate,
                                              item.base_currency_code
                                            )}
                                          </span>
                                        </div>
                                      )}
                                    </div>
                                  ) : (
                                    /* Regular saldo display */
                                    <div className="flex justify-between items-center">
                                      <span className="text-sm font-medium">Saldo:</span>
                                      <span className="font-semibold">
                                        {formatAmountCurrency(item.total_amount || 0, item.original_currency_code || 'IDR')}
                                      </span>
                                    </div>
                                  )}
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    </CollapsibleContent>
                  </Collapsible>
                </Card>
              ))}

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex items-center justify-between">
                  <div className="text-sm text-muted-foreground">
                    Menampilkan {startIndex + 1}-{Math.min(startIndex + itemsPerPage, walletNames.length)} dari {walletNames.length} dompet
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                      disabled={currentPage === 1}
                    >
                      <ChevronLeft className="w-4 h-4" />
                      Sebelumnya
                    </Button>
                    <span className="text-sm">
                      {currentPage} / {totalPages}
                    </span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                      disabled={currentPage === totalPages}
                    >
                      Selanjutnya
                      <ChevronRight className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              )}
            </div>
          ) : (
            <Card className="p-4 text-center text-muted-foreground">
              <p className="text-sm">
                {searchTerm ? `Tidak ada dompet yang cocok dengan "${searchTerm}"` : "Belum ada data ringkasan dompet"}
              </p>
            </Card>
          )}
        </div>
      </div>
    </Card>
  );
};

export default MoneySummaryCard;
